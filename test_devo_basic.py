#!/usr/bin/env python3
"""
Basic script to test Devo connection and pull raw data from one table.
"""

import os
import sys
import logging
from datetime import datetime
from dotenv import load_dotenv

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from tngd_backup.core.devo_client import DevoClient
except ImportError as e:
    print(f"Error importing DevoClient: {e}")
    print("Make sure you're running this from the project root directory")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """Main function to test Devo connection."""

    target_date = "2025-04-08"
    table_name = "my.app.tngd.h3ccoreswitch"  # This table had 72 rows in the backup

    if len(sys.argv) > 1:
        target_date = sys.argv[1]
    if len(sys.argv) > 2:
        table_name = sys.argv[2]

    logger.info(f"Testing Devo connection")
    logger.info(f"Date: {target_date}")
    logger.info(f"Table: {table_name}")
    logger.info("=" * 60)

    try:
        # Initialize client
        logger.info("1. Initializing Devo client...")
        client = DevoClient()
        logger.info("✅ Devo client initialized successfully")

        # Import timestamp helper to create proper WHERE clause
        logger.info("2. Creating timestamp range for date...")
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
        from tngd_backup.utils.timestamp_helper import TimestampHelper

        timestamp_helper = TimestampHelper()
        start_timestamp, end_timestamp = timestamp_helper.date_to_timestamp_range(target_date)

        logger.info(f"Timestamp range: {start_timestamp} to {end_timestamp}")

        # Test simple query with WHERE clause (same as backup system)
        logger.info("3. Testing query with timestamp WHERE clause...")
        query = f"from {table_name} select * where eventdate >= {start_timestamp} and eventdate <= {end_timestamp} limit 10"
        logger.info(f"Query: {query}")

        start_time = datetime.now()
        result = client.execute_query(
            query=query,
            timeout=60,
            table_name=table_name
        )
        end_time = datetime.now()
        
        query_time = (end_time - start_time).total_seconds()
        
        if result is not None:
            logger.info(f"✅ Query successful!")
            logger.info(f"Rows returned: {len(result)}")
            logger.info(f"Query time: {query_time:.2f} seconds")
            
            if len(result) > 0:
                logger.info("")
                logger.info("Sample data (first row):")
                logger.info("-" * 40)
                first_row = result[0]
                for key, value in first_row.items():
                    # Truncate long values for display
                    if isinstance(value, str) and len(value) > 80:
                        display_value = value[:80] + "..."
                    else:
                        display_value = value
                    logger.info(f"{key}: {display_value}")
                
                logger.info("")
                logger.info("Column names:")
                logger.info(f"Total columns: {len(first_row.keys())}")
                for i, col in enumerate(first_row.keys(), 1):
                    logger.info(f"  {i}. {col}")
                    
            else:
                logger.info("⚠️  No data found for this date")
                
        else:
            logger.error("❌ Query returned None")
            
    except Exception as e:
        logger.error(f"❌ Error: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        sys.exit(1)
    
    logger.info("")
    logger.info("Test completed successfully!")

if __name__ == "__main__":
    main()
