#!/usr/bin/env python3
"""
Simple script to test Devo connection and pull sample data from key tables.
"""

import os
import sys
import json
import logging
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from tngd_backup.core.devo_client import DevoClient
except ImportError as e:
    print(f"Error importing DevoClient: {e}")
    print("Make sure you're running this from the project root directory")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_devo_connection():
    """Test basic Devo connection."""
    try:
        logger.info("Initializing Devo client...")
        client = DevoClient()
        logger.info("✅ Devo client initialized successfully")
        return client
    except Exception as e:
        logger.error(f"❌ Failed to initialize Devo client: {e}")
        return None

def test_simple_query(client, table_name, target_date):
    """Test a simple query on a specific table."""
    try:
        # Create simple query
        query = f"from {table_name} select * limit 5"
        
        logger.info(f"Testing query: {query}")
        logger.info(f"Date: {target_date}")
        
        # Execute query
        start_time = datetime.now()
        result = client.execute_query(
            query=query,
            from_date=f"'{target_date}'",
            timeout=60,
            table_name=table_name
        )
        end_time = datetime.now()
        
        query_time = (end_time - start_time).total_seconds()
        
        if result is not None:
            logger.info(f"✅ Query successful: {len(result)} rows in {query_time:.2f}s")
            
            if len(result) > 0:
                logger.info("Sample data (first row):")
                first_row = result[0]
                for key, value in first_row.items():
                    # Truncate long values
                    if isinstance(value, str) and len(value) > 100:
                        value = value[:100] + "..."
                    logger.info(f"  {key}: {value}")
            else:
                logger.info("No data found for this date")
                
            return result
        else:
            logger.warning("❌ Query returned None")
            return None
            
    except Exception as e:
        logger.error(f"❌ Query failed: {e}")
        return None

def test_key_tables(client, target_date):
    """Test a few key tables that showed data in the backup logs."""
    
    # Tables that had significant data in the backup logs
    key_tables = [
        "my.app.tngd.h3ccoreswitch",  # Had 72 rows
        "my.app.tngd.keeper",         # Had 7 rows
        "my.app.tngd.h3cswitch",      # Had 2 rows
        "my.app.tngd.waf",            # Basic table
        "my.app.tngd.actiontraillinux"  # Basic table
    ]
    
    results = {}
    
    logger.info(f"Testing {len(key_tables)} key tables for date {target_date}")
    logger.info("=" * 60)
    
    for i, table in enumerate(key_tables, 1):
        logger.info(f"[{i}/{len(key_tables)}] Testing table: {table}")
        logger.info("-" * 40)
        
        result = test_simple_query(client, table, target_date)
        results[table] = {
            'success': result is not None,
            'row_count': len(result) if result else 0,
            'data': result[:2] if result else []  # Store first 2 rows
        }
        
        logger.info("")
    
    return results

def print_summary(results, target_date):
    """Print summary of results."""
    logger.info("=" * 60)
    logger.info("SUMMARY")
    logger.info("=" * 60)
    logger.info(f"Test Date: {target_date}")
    
    successful = sum(1 for r in results.values() if r['success'])
    with_data = sum(1 for r in results.values() if r['success'] and r['row_count'] > 0)
    total_rows = sum(r['row_count'] for r in results.values())
    
    logger.info(f"Tables tested: {len(results)}")
    logger.info(f"Successful queries: {successful}")
    logger.info(f"Tables with data: {with_data}")
    logger.info(f"Total rows retrieved: {total_rows}")
    logger.info("")
    
    # Show details
    for table, result in results.items():
        status = "✅" if result['success'] else "❌"
        count = result['row_count'] if result['success'] else 0
        logger.info(f"{status} {table}: {count} rows")
    
    logger.info("")

def main():
    """Main function."""
    # Default test date (same as your backup)
    test_date = "2025-04-08"
    
    if len(sys.argv) > 1:
        test_date = sys.argv[1]
    
    logger.info(f"Starting simple Devo test for date: {test_date}")
    logger.info("=" * 60)
    
    # Test connection
    client = test_devo_connection()
    if not client:
        logger.error("Cannot proceed without Devo connection")
        sys.exit(1)
    
    logger.info("")
    
    # Test key tables
    results = test_key_tables(client, test_date)
    
    # Print summary
    print_summary(results, test_date)
    
    # Save results to file
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"devo_simple_test_{test_date}_{timestamp}.json"
    
    try:
        with open(filename, 'w') as f:
            json.dump({
                'test_date': test_date,
                'timestamp': timestamp,
                'results': results
            }, f, indent=2, default=str)
        logger.info(f"Results saved to {filename}")
    except Exception as e:
        logger.error(f"Failed to save results: {e}")
    
    logger.info("Test completed!")

if __name__ == "__main__":
    main()
